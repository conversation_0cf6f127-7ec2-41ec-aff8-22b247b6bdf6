# 🎨 Icon Quick Reference - DassoShu Reader

## 🚨 CRITICAL RULE
**ALWAYS use semantic-first Material Design strategy for ALL icons**

## ✅ DO THIS
```dart
// Semantic naming based on FUNCTION
Icon(AdaptiveIcons.success)    // ✅ What it represents
Icon(AdaptiveIcons.copy)       // ✅ What action it performs  
Icon(AdaptiveIcons.back)       // ✅ What navigation it provides
Icon(AdaptiveIcons.dictionary) // ✅ What tool it represents
```

## ❌ NEVER DO THIS
```dart
// Appearance-based naming or direct usage
Icon(AdaptiveIcons.checkCircle)     // ❌ Describes appearance
Icon(Icons.check_circle)            // ❌ Bypasses system
Icon(CupertinoIcons.checkmark)      // ❌ Causes iOS question marks
```

## 📋 Common Semantic Mappings

### Navigation
- `AdaptiveIcons.back` → Navigate backward
- `AdaptiveIcons.forward` → Navigate forward  
- `AdaptiveIcons.home` → Go to home
- `AdaptiveIcons.close` → Close/dismiss

### Actions
- `AdaptiveIcons.copy` → Copy content
- `AdaptiveIcons.delete` → Remove/delete
- `AdaptiveIcons.edit` → Modify content
- `AdaptiveIcons.save` → Save changes
- `AdaptiveIcons.share` → Share content

### Status
- `AdaptiveIcons.success` → Success state
- `AdaptiveIcons.error` → Error state
- `AdaptiveIcons.warning` → Warning state
- `AdaptiveIcons.info` → Information

### Media
- `AdaptiveIcons.play` → Start playback
- `AdaptiveIcons.pause` → Pause playback
- `AdaptiveIcons.stop` → Stop playback
- `AdaptiveIcons.volume` → Audio control

### Learning App
- `AdaptiveIcons.bookshelf` → Book collection
- `AdaptiveIcons.dictionary` → Language tool
- `AdaptiveIcons.vocabulary` → Vocabulary book
- `AdaptiveIcons.hsk` → Education/learning
- `AdaptiveIcons.notes` → Note-taking

## 🔧 Implementation Steps

### 1. Check if icon exists
```dart
// Look in lib/config/adaptive_icons.dart
static IconData get yourSemanticName => Icons.material_icon;
```

### 2. If not exists, add it
```dart
/// Platform-appropriate [semantic name] icon
/// Using Material Design for iOS compatibility
static IconData get yourSemanticName => Icons.material_icon;
```

### 3. Use semantic name
```dart
Icon(AdaptiveIcons.yourSemanticName)
```

## 🚫 Anti-Patterns

### Never use CupertinoIcons
```dart
// ❌ Causes iOS question marks
if (PlatformAdaptations.isIOS) {
  return CupertinoIcons.some_icon;
}
```

### Never bypass AdaptiveIcons
```dart
// ❌ Direct usage
Icon(Icons.check_circle)

// ✅ Use semantic token
Icon(AdaptiveIcons.success)
```

### Never use appearance-based naming
```dart
// ❌ Describes how it looks
AdaptiveIcons.checkCircle
AdaptiveIcons.arrowBack

// ✅ Describes what it does
AdaptiveIcons.success
AdaptiveIcons.back
```

## 📖 Full Documentation
- [Semantic-First Icon Strategy](SEMANTIC_FIRST_ICON_STRATEGY.md)
- [Cross-Platform Development Guide](CROSS_PLATFORM_DEVELOPMENT_GUIDE.md)
- [Cross-Platform Fix Patterns](cross-platform-fix-patterns.md)

---
**Remember**: Function over form, semantics over appearance!
