import 'package:dasso_reader/utils/log/common.dart';
import 'package:dasso_reader/utils/platform/cross_platform_validator.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';

void webviewConsoleMessage(
  InAppWebViewController controller,
  ConsoleMessage consoleMessage,
) {
  // Validate WebView support before processing console messages
  if (!CrossPlatformValidator.isWebViewSupported()) {
    AnxLog.warning('WebView not supported - ignoring console message');
    return;
  }

  if (consoleMessage.message.contains(
    'An iframe which has both allow-scripts and allow-same-origin for its sandbox attribute can escape its sandboxing',
  )) {
    return;
  }
  if (consoleMessage.messageLevel == ConsoleMessageLevel.LOG) {
    AnxLog.info('Webview: ${consoleMessage.message}');
  } else if (consoleMessage.messageLevel == ConsoleMessageLevel.WARNING) {
    AnxLog.warning('Webview: ${consoleMessage.message}');
  } else if (consoleMessage.messageLevel == ConsoleMessageLevel.ERROR) {
    AnxLog.severe('Webview: ${consoleMessage.message}');
  }
}
