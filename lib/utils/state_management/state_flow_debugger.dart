import 'dart:async';
import 'dart:collection';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:dasso_reader/utils/log/common.dart';
import 'package:dasso_reader/config/responsive_system.dart';
import 'package:dasso_reader/config/design_system.dart';

/// Comprehensive state flow debugging system for Dasso Reader
///
/// This system provides:
/// - Visual state change tracking and dependency mapping
/// - Real-time state flow monitoring
/// - State change history with rollback capabilities
/// - Provider interaction tracking
/// - Debug overlay for development

/// State change event data
class StateChangeEvent {
  final String providerId;
  final String providerType;
  final dynamic previousValue;
  final dynamic newValue;
  final DateTime timestamp;
  final StackTrace? stackTrace;
  final List<String> dependencies;
  final String? triggeredBy;

  const StateChangeEvent({
    required this.providerId,
    required this.providerType,
    required this.previousValue,
    required this.newValue,
    required this.timestamp,
    this.stackTrace,
    this.dependencies = const [],
    this.triggeredBy,
  });

  Map<String, dynamic> toJson() => {
        'providerId': providerId,
        'providerType': providerType,
        'previousValue': previousValue?.toString(),
        'newValue': newValue?.toString(),
        'timestamp': timestamp.toIso8601String(),
        'dependencies': dependencies,
        'triggeredBy': triggeredBy,
      };
}

/// Provider dependency information
class ProviderDependency {
  final String providerId;
  final String dependsOn;
  final String dependencyType; // 'watch', 'read', 'listen'
  final DateTime establishedAt;

  const ProviderDependency({
    required this.providerId,
    required this.dependsOn,
    required this.dependencyType,
    required this.establishedAt,
  });
}

/// State flow debugging configuration
class StateFlowDebugConfig {
  final bool enabled;
  final bool trackDependencies;
  final bool enableVisualizer;
  final bool enableOverlay;
  final int maxHistoryEntries;
  final Duration historyRetention;
  final Set<String> trackedProviders;
  final bool enableStackTraces;

  const StateFlowDebugConfig({
    this.enabled = kDebugMode,
    this.trackDependencies = true,
    this.enableVisualizer = true,
    this.enableOverlay = true,
    this.maxHistoryEntries = 1000,
    this.historyRetention = const Duration(minutes: 30),
    this.trackedProviders = const {},
    this.enableStackTraces = false,
  });
}

/// Main state flow debugger
class StateFlowDebugger {
  static StateFlowDebugger? _instance;
  static StateFlowDebugger get instance => _instance ??= StateFlowDebugger._();

  StateFlowDebugger._();

  StateFlowDebugConfig _config = const StateFlowDebugConfig();
  final Queue<StateChangeEvent> _stateHistory = Queue<StateChangeEvent>();
  final Map<String, List<ProviderDependency>> _dependencies = {};
  final Map<String, dynamic> _currentStates = {};
  final StreamController<StateChangeEvent> _stateChangeStream =
      StreamController<StateChangeEvent>.broadcast();

  Timer? _cleanupTimer;
  bool _initialized = false;

  /// Initialize the state flow debugger
  void initialize({StateFlowDebugConfig? config}) {
    if (_initialized) return;

    _config = config ?? const StateFlowDebugConfig();

    if (!_config.enabled) return;

    // Start periodic cleanup
    _cleanupTimer = Timer.periodic(const Duration(minutes: 5), (_) {
      _cleanupOldEntries();
    });

    _initialized = true;

    if (kDebugMode) {
      AnxLog.info('🔍 State Flow Debugger initialized');
    }
  }

  /// Track a state change
  void trackStateChange({
    required String providerId,
    required String providerType,
    required dynamic previousValue,
    required dynamic newValue,
    List<String> dependencies = const [],
    String? triggeredBy,
  }) {
    if (!_config.enabled) return;

    // Check if we should track this provider
    if (_config.trackedProviders.isNotEmpty &&
        !_config.trackedProviders.contains(providerId)) {
      return;
    }

    final event = StateChangeEvent(
      providerId: providerId,
      providerType: providerType,
      previousValue: previousValue,
      newValue: newValue,
      timestamp: DateTime.now(),
      stackTrace: _config.enableStackTraces ? StackTrace.current : null,
      dependencies: dependencies,
      triggeredBy: triggeredBy,
    );

    // Add to history
    _stateHistory.add(event);
    _currentStates[providerId] = newValue;

    // Limit history size
    while (_stateHistory.length > _config.maxHistoryEntries) {
      _stateHistory.removeFirst();
    }

    // Notify listeners
    _stateChangeStream.add(event);

    if (kDebugMode) {
      _logStateChange(event);
    }
  }

  /// Track provider dependency
  void trackDependency({
    required String providerId,
    required String dependsOn,
    required String dependencyType,
  }) {
    if (!_config.enabled || !_config.trackDependencies) return;

    final dependency = ProviderDependency(
      providerId: providerId,
      dependsOn: dependsOn,
      dependencyType: dependencyType,
      establishedAt: DateTime.now(),
    );

    _dependencies.putIfAbsent(providerId, () => []).add(dependency);

    if (kDebugMode) {
      debugPrint('🔗 Dependency: $providerId $dependencyType $dependsOn');
    }
  }

  /// Get state change history
  List<StateChangeEvent> getStateHistory({
    String? providerId,
    Duration? since,
  }) {
    var history = _stateHistory.toList();

    if (providerId != null) {
      history = history.where((e) => e.providerId == providerId).toList();
    }

    if (since != null) {
      final cutoff = DateTime.now().subtract(since);
      history = history.where((e) => e.timestamp.isAfter(cutoff)).toList();
    }

    return history;
  }

  /// Get provider dependencies
  List<ProviderDependency> getDependencies(String providerId) {
    return _dependencies[providerId] ?? [];
  }

  /// Get all tracked providers
  Set<String> getTrackedProviders() {
    return _currentStates.keys.toSet();
  }

  /// Get current state of a provider
  dynamic getCurrentState(String providerId) {
    return _currentStates[providerId];
  }

  /// Get state change stream
  Stream<StateChangeEvent> get stateChangeStream => _stateChangeStream.stream;

  /// Generate dependency graph
  Map<String, List<String>> generateDependencyGraph() {
    final graph = <String, List<String>>{};

    for (final entry in _dependencies.entries) {
      final providerId = entry.key;
      final dependencies = entry.value.map((d) => d.dependsOn).toList();
      graph[providerId] = dependencies;
    }

    return graph;
  }

  /// Clear all tracking data
  void clear() {
    _stateHistory.clear();
    _dependencies.clear();
    _currentStates.clear();

    if (kDebugMode) {
      AnxLog.info('🧹 State Flow Debugger cleared');
    }
  }

  /// Dispose resources
  void dispose() {
    _cleanupTimer?.cancel();
    _stateChangeStream.close();
    _initialized = false;
  }

  /// Clean up old entries
  void _cleanupOldEntries() {
    final cutoff = DateTime.now().subtract(_config.historyRetention);
    _stateHistory.removeWhere((event) => event.timestamp.isBefore(cutoff));
  }

  /// Log state change for debugging
  void _logStateChange(StateChangeEvent event) {
    final hasChanged = event.previousValue != event.newValue;
    if (!hasChanged) return;

    final icon = _getProviderIcon(event.providerType);
    final trigger =
        event.triggeredBy != null ? ' (triggered by ${event.triggeredBy})' : '';

    debugPrint('$icon State Change: ${event.providerId}$trigger\n'
        '  Previous: ${event.previousValue}\n'
        '  New: ${event.newValue}\n'
        '  Dependencies: ${event.dependencies.join(', ')}');
  }

  /// Get icon for provider type
  String _getProviderIcon(String providerType) {
    switch (providerType.toLowerCase()) {
      case 'book':
      case 'booklist':
        return '📚';
      case 'ai':
      case 'aichat':
        return '🤖';
      case 'reading':
        return '📖';
      case 'settings':
      case 'prefs':
        return '⚙️';
      case 'hsk':
        return '🎓';
      case 'dictionary':
        return '📖';
      default:
        return '🔄';
    }
  }
}

/// State change tracker mixin for providers
mixin StateChangeTracker<T> on StateNotifier<T> {
  String get providerId;
  String get providerType => runtimeType.toString();

  @override
  set state(T newState) {
    final previousState = state;
    super.state = newState;

    StateFlowDebugger.instance.trackStateChange(
      providerId: providerId,
      providerType: providerType,
      previousValue: previousState,
      newValue: newState,
    );
  }
}

/// Provider tracking utilities
class ProviderTracker {
  /// Track provider creation
  static void trackProviderCreation(String providerId, String? providerType) {
    StateFlowDebugger.instance.trackStateChange(
      providerId: providerId,
      providerType: providerType ?? 'Provider',
      previousValue: null,
      newValue: 'created',
    );
  }

  /// Create a tracked provider
  static Provider<T> createTrackedProvider<T>(
    T Function(Ref ref) create, {
    String? name,
    required String providerId,
    String? providerType,
  }) {
    // Track provider creation
    trackProviderCreation(providerId, providerType);

    return Provider<T>(
      (ref) {
        final value = create(ref);

        // Track state changes
        StateFlowDebugger.instance.trackStateChange(
          providerId: providerId,
          providerType: providerType ?? 'Provider',
          previousValue: null,
          newValue: value,
        );

        return value;
      },
      name: name,
    );
  }
}

/// State flow visualizer for generating dependency graphs
class StateFlowVisualizer {
  static StateFlowVisualizer? _instance;
  static StateFlowVisualizer get instance =>
      _instance ??= StateFlowVisualizer._();

  StateFlowVisualizer._();

  /// Generate Mermaid diagram for state dependencies
  String generateMermaidDiagram() {
    final debugger = StateFlowDebugger.instance;
    final dependencyGraph = debugger.generateDependencyGraph();
    final trackedProviders = debugger.getTrackedProviders();

    final buffer = StringBuffer();
    buffer.writeln('graph TD');

    // Add nodes for all providers
    for (final providerId in trackedProviders) {
      final icon = _getProviderIcon(providerId);
      buffer.writeln('    $providerId["$icon $providerId"]');
    }

    // Add dependency edges
    for (final entry in dependencyGraph.entries) {
      final providerId = entry.key;
      final dependencies = entry.value;

      for (final dependency in dependencies) {
        buffer.writeln('    $dependency --> $providerId');
      }
    }

    // Add styling
    buffer.writeln('    classDef bookProvider fill:#e1f5fe');
    buffer.writeln('    classDef aiProvider fill:#f3e5f5');
    buffer.writeln('    classDef readingProvider fill:#e8f5e8');
    buffer.writeln('    classDef settingsProvider fill:#fff3e0');

    // Apply styles
    for (final providerId in trackedProviders) {
      if (providerId.toLowerCase().contains('book')) {
        buffer.writeln('    class $providerId bookProvider');
      } else if (providerId.toLowerCase().contains('ai')) {
        buffer.writeln('    class $providerId aiProvider');
      } else if (providerId.toLowerCase().contains('reading')) {
        buffer.writeln('    class $providerId readingProvider');
      } else if (providerId.toLowerCase().contains('settings')) {
        buffer.writeln('    class $providerId settingsProvider');
      }
    }

    return buffer.toString();
  }

  /// Generate state flow timeline
  List<Map<String, dynamic>> generateStateTimeline({Duration? since}) {
    final debugger = StateFlowDebugger.instance;
    final history = debugger.getStateHistory(since: since);

    return history
        .map(
          (event) => {
            'timestamp': event.timestamp.toIso8601String(),
            'providerId': event.providerId,
            'providerType': event.providerType,
            'hasChanged': event.previousValue != event.newValue,
            'dependencies': event.dependencies,
            'triggeredBy': event.triggeredBy,
          },
        )
        .toList();
  }

  /// Get provider icon for visualization
  String _getProviderIcon(String providerId) {
    if (providerId.toLowerCase().contains('book')) return '📚';
    if (providerId.toLowerCase().contains('ai')) return '🤖';
    if (providerId.toLowerCase().contains('reading')) return '📖';
    if (providerId.toLowerCase().contains('settings')) return '⚙️';
    if (providerId.toLowerCase().contains('hsk')) return '🎓';
    if (providerId.toLowerCase().contains('dictionary')) return '📖';
    return '🔄';
  }
}

/// Debug overlay widget for real-time state monitoring
class StateFlowDebugOverlay extends StatefulWidget {
  final Widget child;
  final bool enabled;

  const StateFlowDebugOverlay({
    super.key,
    required this.child,
    this.enabled = kDebugMode,
  });

  @override
  State<StateFlowDebugOverlay> createState() => _StateFlowDebugOverlayState();
}

class _StateFlowDebugOverlayState extends State<StateFlowDebugOverlay> {
  bool _showOverlay = false;
  late StreamSubscription<StateChangeEvent> _stateChangeSubscription;
  final List<StateChangeEvent> _recentChanges = [];

  @override
  void initState() {
    super.initState();
    if (widget.enabled) {
      _stateChangeSubscription =
          StateFlowDebugger.instance.stateChangeStream.listen((event) {
        if (mounted) {
          setState(() {
            _recentChanges.add(event);
            // Keep only last 10 changes
            if (_recentChanges.length > 10) {
              _recentChanges.removeAt(0);
            }
          });
        }
      });
    }
  }

  @override
  void dispose() {
    _stateChangeSubscription.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.enabled) {
      return widget.child;
    }

    return Stack(
      children: [
        widget.child,
        if (_showOverlay) _buildDebugOverlay(),
        _buildToggleButton(),
      ],
    );
  }

  Widget _buildToggleButton() {
    return Positioned(
      top: ResponsiveSystem.getScreenPadding(context).top + 10,
      right: 10,
      child: FloatingActionButton.small(
        onPressed: () => setState(() => _showOverlay = !_showOverlay),
        backgroundColor: Colors.blue.withValues(alpha: 0.8),
        child: Icon(_showOverlay ? Icons.close : Icons.bug_report),
      ),
    );
  }

  Widget _buildDebugOverlay() {
    return Positioned(
      top: ResponsiveSystem.getScreenPadding(context).top + 70,
      right: 10,
      child: Container(
        width: 300,
        height: 400,
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.9),
          borderRadius: BorderRadius.circular(DesignSystem.radiusM),
          border: Border.all(color: Colors.blue),
        ),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(DesignSystem.spaceS),
              decoration: const BoxDecoration(
                color: Colors.blue,
                borderRadius: BorderRadius.vertical(top: Radius.circular(8)),
              ),
              child: const Row(
                children: [
                  Icon(Icons.bug_report,
                      color: Colors.white,
                      size: DesignSystem.widgetIconSizeSmall),
                  SizedBox(width: DesignSystem.spaceS),
                  Text(
                    'State Flow Debug',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.all(DesignSystem.spaceS),
                itemCount: _recentChanges.length,
                itemBuilder: (context, index) {
                  final event = _recentChanges[index];
                  final hasChanged = event.previousValue != event.newValue;

                  return Container(
                    margin: const EdgeInsets.only(bottom: DesignSystem.spaceXS),
                    padding: const EdgeInsets.all(DesignSystem.spaceXS),
                    decoration: BoxDecoration(
                      color: hasChanged
                          ? Colors.green.withValues(alpha: 0.2)
                          : Colors.grey.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(DesignSystem.radiusS),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '${_getProviderIcon(event.providerType)} ${event.providerId}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: DesignSystem.fontSizeS,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          event.timestamp
                              .toLocal()
                              .toString()
                              .substring(11, 19),
                          style: TextStyle(
                              color: Colors.grey[400],
                              fontSize: DesignSystem.fontSizeXS),
                        ),
                        if (hasChanged)
                          Text(
                            'Changed',
                            style: TextStyle(
                              color: Colors.green[300],
                              fontSize: DesignSystem.fontSizeXS,
                            ),
                          ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getProviderIcon(String providerType) {
    switch (providerType.toLowerCase()) {
      case 'book':
      case 'booklist':
        return '📚';
      case 'ai':
      case 'aichat':
        return '🤖';
      case 'reading':
        return '📖';
      case 'settings':
      case 'prefs':
        return '⚙️';
      case 'hsk':
        return '🎓';
      case 'dictionary':
        return '📖';
      default:
        return '🔄';
    }
  }
}
