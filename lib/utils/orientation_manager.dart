import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../config/responsive_system.dart';
import '../config/design_system.dart';

/// Comprehensive orientation management system for Dasso Reader
///
/// This class provides:
/// - State preservation during orientation changes
/// - Orientation-specific UI adaptations
/// - Smooth transition handling
/// - Reading interface optimizations
class OrientationManager {
  OrientationManager._();

  // =====================================================
  // DEVICE-AWARE ORIENTATION CONSTRAINTS
  // =====================================================

  /// Apply optimal orientation constraints based on device type
  ///
  /// Strategy:
  /// - Mobile devices: Portrait-only for optimal Chinese reading experience
  /// - Tablet devices: Both orientations for flexible reading positions
  ///
  /// This method is safe to call multiple times and will only apply constraints
  /// when necessary to avoid unnecessary system calls.
  static Future<void> applyOptimalOrientationConstraints(
    BuildContext context,
  ) async {
    try {
      final orientations = _getOptimalOrientations(context);
      final isTablet = DesignSystem.isTablet(context);

      // Apply orientation constraints
      await SystemChrome.setPreferredOrientations(orientations);

      // Log the applied strategy for debugging
      debugPrint(
          'OrientationManager: Applied ${isTablet ? 'tablet' : 'mobile'} orientation constraints: '
          '${orientations.map((o) => o.toString().split('.').last).join(', ')}');
    } catch (e) {
      // Graceful fallback - continue without orientation constraints
      debugPrint(
        'OrientationManager: Failed to apply orientation constraints - $e',
      );
    }
  }

  /// Get optimal orientations based on device type and user preferences
  static List<DeviceOrientation> _getOptimalOrientations(BuildContext context) {
    // TODO: Add user preference override in settings
    // For now, use device-specific strategy

    // Apply device-specific strategy
    if (DesignSystem.isTablet(context)) {
      // Tablets: Allow both orientations for flexible reading
      return [
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ];
    } else {
      // Mobile devices: Portrait-only for optimal Chinese reading
      return [
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
      ];
    }
  }

  /// Check if current device should use portrait-only orientation
  static bool shouldUsePortraitOnly(BuildContext context) {
    // TODO: Add user preference check when implemented
    return !DesignSystem.isTablet(context);
  }

  /// Reset orientation constraints to allow all orientations
  /// Useful for specific screens that need flexibility (e.g., image viewer)
  static Future<void> resetOrientationConstraints() async {
    try {
      await SystemChrome.setPreferredOrientations(DeviceOrientation.values);
    } catch (e) {
      debugPrint(
        'OrientationManager: Failed to reset orientation constraints - $e',
      );
    }
  }

  // =====================================================
  // STATE PRESERVATION
  // =====================================================

  /// Preserves scroll position during orientation changes
  static void preserveScrollPosition(
    ScrollController controller,
    VoidCallback onOrientationChange,
  ) {
    final currentOffset = controller.hasClients ? controller.offset : 0.0;

    onOrientationChange();

    // Restore scroll position after orientation change
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (controller.hasClients) {
        controller.animateTo(
          currentOffset,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    });
  }

  /// Creates a PageStorageKey for preserving widget state
  static PageStorageKey<String> createStorageKey(String identifier) {
    return PageStorageKey<String>('orientation_$identifier');
  }

  /// Preserves form state during orientation changes
  static Map<String, dynamic> preserveFormState(
    List<TextEditingController> controllers,
  ) {
    final state = <String, dynamic>{};
    for (int i = 0; i < controllers.length; i++) {
      state['controller_$i'] = controllers[i].text;
    }
    return state;
  }

  /// Restores form state after orientation change
  static void restoreFormState(
    List<TextEditingController> controllers,
    Map<String, dynamic> state,
  ) {
    for (int i = 0; i < controllers.length; i++) {
      final text = state['controller_$i'] as String?;
      if (text != null) {
        controllers[i].text = text;
      }
    }
  }

  // =====================================================
  // ORIENTATION DETECTION & HANDLING
  // =====================================================

  /// Checks if orientation change is significant enough to trigger layout rebuild
  static bool isSignificantOrientationChange(
    Orientation oldOrientation,
    Orientation newOrientation,
    BuildContext context,
  ) {
    if (oldOrientation == newOrientation) return false;

    // Always significant for mobile and tablet
    if (DesignSystem.isMobile(context) || DesignSystem.isTablet(context)) {
      return true;
    }

    // Less significant for desktop
    return false;
  }

  /// Gets the appropriate system UI overlay style for current orientation
  static SystemUiOverlayStyle getOrientationAwareSystemUIStyle(
    BuildContext context, {
    bool isReading = false,
  }) {
    final isLandscape = ResponsiveSystem.isLandscape(context);
    final brightness = Theme.of(context).brightness;

    if (isReading) {
      // Reading mode: minimal UI
      return SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness:
            brightness == Brightness.dark ? Brightness.light : Brightness.dark,
        systemNavigationBarColor: Colors.transparent,
        systemNavigationBarIconBrightness:
            brightness == Brightness.dark ? Brightness.light : Brightness.dark,
      );
    } else if (isLandscape && DesignSystem.isMobile(context)) {
      // Mobile landscape: more immersive
      return SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness:
            brightness == Brightness.dark ? Brightness.light : Brightness.dark,
        systemNavigationBarColor: Theme.of(context).scaffoldBackgroundColor,
        systemNavigationBarIconBrightness:
            brightness == Brightness.dark ? Brightness.light : Brightness.dark,
      );
    } else {
      // Standard UI
      return SystemUiOverlayStyle(
        statusBarColor: Theme.of(context).appBarTheme.backgroundColor,
        statusBarIconBrightness:
            brightness == Brightness.dark ? Brightness.light : Brightness.dark,
        systemNavigationBarColor: Theme.of(context).scaffoldBackgroundColor,
        systemNavigationBarIconBrightness:
            brightness == Brightness.dark ? Brightness.light : Brightness.dark,
      );
    }
  }

  // =====================================================
  // LAYOUT ADAPTATIONS
  // =====================================================

  /// Determines if a widget should use horizontal layout in current orientation
  static bool shouldUseHorizontalLayout(
    BuildContext context, {
    int minWidth = 600,
  }) {
    final screenWidth = ResponsiveSystem.getScreenWidth(context);
    final isLandscape = ResponsiveSystem.isLandscape(context);

    return isLandscape && screenWidth >= minWidth;
  }

  /// Gets orientation-appropriate flex values for responsive layouts
  static List<int> getOrientationAwareFlexValues(BuildContext context) {
    final isLandscape = ResponsiveSystem.isLandscape(context);

    if (DesignSystem.isDesktop(context)) {
      return isLandscape
          ? [2, 3]
          : [1, 2]; // More space for content in landscape
    } else if (DesignSystem.isTablet(context)) {
      return isLandscape ? [1, 2] : [1, 1]; // Balanced layout
    } else {
      return [1, 1]; // Equal distribution for mobile
    }
  }

  /// Creates orientation-aware constraints for reading interface
  static BoxConstraints getReadingInterfaceConstraints(BuildContext context) {
    final screenSize = ResponsiveSystem.getScreenSize(context);
    final isLandscape = ResponsiveSystem.isLandscape(context);

    if (isLandscape) {
      // In landscape, optimize for reading width
      return BoxConstraints(
        maxWidth: screenSize.width * 0.8,
        minWidth: 320,
      );
    } else {
      // In portrait, use full width with padding
      return BoxConstraints(
        maxWidth: screenSize.width,
        minWidth: 280,
      );
    }
  }

  // =====================================================
  // TRANSITION HELPERS
  // =====================================================

  /// Creates a smooth transition for orientation changes
  static Widget createOrientationTransition({
    required Widget child,
    required Duration duration,
  }) {
    return AnimatedSwitcher(
      duration: duration,
      transitionBuilder: (child, animation) {
        return FadeTransition(
          opacity: animation,
          child: child,
        );
      },
      child: child,
    );
  }

  /// Delays execution until after orientation change is complete
  static void executeAfterOrientationChange(VoidCallback callback) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Add small delay to ensure orientation change is complete
      Future.delayed(const Duration(milliseconds: 100), callback);
    });
  }

  // =====================================================
  // READING INTERFACE OPTIMIZATIONS
  // =====================================================

  /// Gets optimal reading column width for current orientation
  static double getOptimalReadingColumnWidth(BuildContext context) {
    final screenWidth = ResponsiveSystem.getScreenWidth(context);
    final isLandscape = ResponsiveSystem.isLandscape(context);

    if (isLandscape && screenWidth > 800) {
      // In landscape on larger screens, use optimal reading width
      return 600.0;
    } else if (isLandscape) {
      // In landscape on smaller screens, use most of the width
      return screenWidth * 0.8;
    } else {
      // In portrait, use full width with padding
      return screenWidth - 32.0;
    }
  }

  /// Determines if reading interface should use two-column layout
  static bool shouldUseTwoColumnReading(BuildContext context) {
    final screenWidth = ResponsiveSystem.getScreenWidth(context);
    final isLandscape = ResponsiveSystem.isLandscape(context);

    return isLandscape &&
        screenWidth > 1000 &&
        (DesignSystem.isDesktop(context) || DesignSystem.isTablet(context));
  }
}
