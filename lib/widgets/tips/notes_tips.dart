import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/l10n/generated/L10n.dart';
import 'package:flutter/material.dart';

class NotesTips extends StatelessWidget {
  const NotesTips({super.key});

  final TextStyle textStyleBig = const TextStyle(
    fontSize: DesignSystem.fontSizeL,
    fontWeight: FontWeight.bold,
  );
  final TextStyle textStyle = const TextStyle(
    fontSize: DesignSystem.fontSizeM,
  );

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Text(
            'o(TヘTo) ',
            style: TextStyle(
              fontSize: DesignSystem.fontSizeDisplayL,
              fontWeight: FontWeight.bold,
              color: Colors.grey,
            ),
          ),
          const SizedBox(
              height: DesignSystem.spaceXXL + DesignSystem.spaceTiny),
          Text(
            L10n.of(context).notes_tips_1,
            style: textStyleBig,
          ),
          const SizedBox(height: DesignSystem.spaceS + DesignSystem.spaceTiny),
          Text(
            L10n.of(context).notes_tips_2,
            style: textStyle,
          ),
        ],
      ),
    );
  }
}
