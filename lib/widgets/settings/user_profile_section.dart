import 'package:dasso_reader/config/shared_preference_provider.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:dasso_reader/config/adaptive_icons.dart';
import 'package:dasso_reader/widgets/common/adaptive_components.dart';

class UserProfileSection extends StatelessWidget {
  const UserProfileSection({super.key});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final prefsNotifier = Provider.of<Prefs>(context);

    // Get username from preferences or use default
    final username = prefsNotifier.username ?? 'Reader';

    return Padding(
      padding: DesignSystem.pagePadding,
      child: Row(
        children: [
          // Avatar
          CircleAvatar(
            radius: DesignSystem.spaceXXL,
            backgroundColor: colorScheme.primaryContainer,
            child: Icon(
              AdaptiveIcons.person,
              size: DesignSystem.getAdjustedIconSize(DesignSystem.spaceXXL),
              color: colorScheme.onPrimaryContainer,
            ),
          ),
          DesignSystem
              .horizontalSpaceM, // Replaces const SizedBox(width: DesignSystem.spaceM)
          // User info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  username,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight:
                            DesignSystem.getAdjustedFontWeight(FontWeight.bold),
                        color: DesignSystem.getSettingsTextColor(
                          context,
                          isPrimary: true,
                        ),
                      ),
                ),
                DesignSystem.verticalSpaceXS,
                Text(
                  'Chinese Language Learner',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: DesignSystem.getSettingsTextColor(
                          context,
                          isPrimary: false,
                        ),
                      ),
                ),
              ],
            ),
          ),
          // Edit button
          IconButton(
            icon: Icon(
              AdaptiveIcons.edit,
              size: DesignSystem.getAdjustedIconSize(
                  DesignSystem.widgetIconSizeMedium),
              semanticLabel: 'Edit profile',
            ),
            tooltip: 'Edit profile',
            onPressed: () {
              // Show dialog to edit username
              showDialog<void>(
                context: context,
                builder: (context) => _buildEditProfileDialog(context),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildEditProfileDialog(BuildContext context) {
    final prefsNotifier = Provider.of<Prefs>(context, listen: false);
    final TextEditingController controller =
        TextEditingController(text: prefsNotifier.username ?? 'Reader');

    return AlertDialog(
      title: Text(
        'Edit Profile',
        style: TextStyle(
          color: DesignSystem.getSettingsTextColor(context, isPrimary: true),
        ),
      ),
      content: TextField(
        controller: controller,
        decoration: const InputDecoration(
          labelText: 'Username',
          hintText: 'Enter your name',
        ),
        autofocus: true,
      ),
      actions: [
        AdaptiveDialogAction(
          text: 'Cancel',
          onPressed: () => Navigator.pop(context),
        ).toMaterialAction(),
        AdaptiveDialogAction(
          text: 'Save',
          isDefaultAction: true,
          onPressed: () {
            // Save username
            prefsNotifier.saveUsername(controller.text);
            Navigator.pop(context);
          },
        ).toMaterialAction(),
      ],
    );
  }
}
