import 'package:dasso_reader/config/shared_preference_provider.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/l10n/generated/L10n.dart';
import 'package:dasso_reader/models/book.dart';
import 'package:dasso_reader/providers/book_list.dart';
import 'package:dasso_reader/utils/accessibility/semantic_helpers.dart';
import 'package:dasso_reader/widgets/bookshelf/book_item.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:dasso_reader/widgets/common/adaptive_components.dart';

class BookOpenedFolder extends ConsumerStatefulWidget {
  const BookOpenedFolder({super.key, required this.books});
  final List<Book> books;

  @override
  ConsumerState<BookOpenedFolder> createState() => _BookOpenedFolderState();
}

class _BookOpenedFolderState extends ConsumerState<BookOpenedFolder> {
  bool isEditing = false;
  List<Book> books = [];

  @override
  void initState() {
    super.initState();
    books = widget.books;
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      content: SizedBox(
        width: MediaQuery.of(context).size.width * 0.7,
        child: GridView.builder(
          shrinkWrap: true,
          gridDelegate: SliverGridDelegateWithMaxCrossAxisExtent(
            maxCrossAxisExtent: Prefs().bookCoverWidth,
            childAspectRatio: 1 / 1.9,
            crossAxisSpacing: DesignSystem.spaceS,
            mainAxisSpacing: DesignSystem.spaceS,
          ),
          itemCount: books.length,
          itemBuilder: (context, index) => Stack(
            children: [
              BookItem(book: books[index]),
              isEditing
                  ? Positioned(
                      right: 0,
                      top: 0,
                      child: SemanticHelpers.button(
                        context: context,
                        label: 'Remove ${books[index].title} from folder',
                        hint: 'Remove this book from the current folder',
                        onTap: () {
                          ref
                              .read(bookListProvider.notifier)
                              .removeFromGroup(books[index]);
                          books.removeAt(index);
                          if (books.isEmpty) {
                            Navigator.pop(context);
                          }
                          setState(() {});
                        },
                        child: IconButton(
                          onPressed: () {
                            ref
                                .read(bookListProvider.notifier)
                                .removeFromGroup(books[index]);
                            books.removeAt(index);
                            if (books.isEmpty) {
                              Navigator.pop(context);
                            }
                            setState(() {});
                          },
                          icon: Icon(
                            Icons.remove_circle,
                            size: DesignSystem.getAdjustedIconSize(
                              DesignSystem.widgetIconSizeLarge,
                            ),
                            color: Colors.red,
                          ),
                        ),
                      ),
                    )
                  : Container(),
            ],
          ),
        ),
      ),
      actions: [
        SemanticHelpers.button(
          context: context,
          label: L10n.of(context).common_dissolve,
          hint: 'Dissolve this folder and ungroup all books',
          onTap: () {
            ref.read(bookListProvider.notifier).dissolveGroup(books);
            Navigator.pop(context);
          },
          child: AdaptiveDialogAction(
            text: L10n.of(context).common_dissolve,
            isDestructive: true,
            onPressed: () {
              ref.read(bookListProvider.notifier).dissolveGroup(books);
              Navigator.pop(context);
            },
          ).toMaterialAction(),
        ),
        SemanticHelpers.button(
          context: context,
          label: isEditing
              ? L10n.of(context).common_cancel
              : L10n.of(context).common_edit,
          hint: isEditing
              ? 'Cancel editing mode'
              : 'Enter editing mode to remove books from folder',
          onTap: () {
            isEditing = !isEditing;
            setState(() {});
          },
          child: AdaptiveDialogAction(
            text: isEditing
                ? L10n.of(context).common_cancel
                : L10n.of(context).common_edit,
            onPressed: () {
              isEditing = !isEditing;
              setState(() {});
            },
          ).toMaterialAction(),
        ),
      ],
    );
  }
}
