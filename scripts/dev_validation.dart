#!/usr/bin/env dart

/// Development-time validation script for DassoShu Reader
///
/// Provides real-time feedback on cross-platform compatibility issues
/// and design system compliance during development.

import 'dart:io';
import 'dart:convert';

void main(List<String> args) async {
  print('🔧 DassoShu Reader Development Validator');
  print('========================================');

  final validator = DevValidator();

  if (args.contains('--help') || args.contains('-h')) {
    validator.printHelp();
    return;
  }

  final watch = args.contains('--watch') || args.contains('-w');
  final fix = args.contains('--fix');
  final verbose = args.contains('--verbose') || args.contains('-v');

  try {
    if (watch) {
      await validator.watchMode(fix: fix, verbose: verbose);
    } else {
      await validator.validateOnce(fix: fix, verbose: verbose);
    }
  } catch (e) {
    print('❌ Validation failed: $e');
    exit(1);
  }
}

class DevValidator {
  static const String _libPath = 'lib';
  static const Duration _watchInterval = Duration(seconds: 2);

  /// Run validation once
  Future<void> validateOnce({bool fix = false, bool verbose = false}) async {
    print('🔍 Running development validation...');

    final results = await _runValidation(fix: fix, verbose: verbose);
    _printResults(results);

    if (results.hasErrors) {
      exit(1);
    }
  }

  /// Watch mode - continuously monitor files
  Future<void> watchMode({bool fix = false, bool verbose = false}) async {
    print('👀 Starting watch mode...');
    print('Press Ctrl+C to stop');

    final Map<String, DateTime> lastModified = {};

    while (true) {
      try {
        final dartFiles = await _findDartFiles();
        bool hasChanges = false;

        for (final file in dartFiles) {
          final stat = await file.stat();
          final lastMod = lastModified[file.path];

          if (lastMod == null || stat.modified.isAfter(lastMod)) {
            lastModified[file.path] = stat.modified;
            hasChanges = true;
          }
        }

        if (hasChanges) {
          print('\n🔄 Changes detected, validating...');
          final results = await _runValidation(fix: fix, verbose: verbose);
          _printResults(results);
        }

        await Future.delayed(_watchInterval);
      } catch (e) {
        print('⚠️ Watch error: $e');
        await Future.delayed(_watchInterval);
      }
    }
  }

  /// Run all validation checks
  Future<ValidationResults> _runValidation({
    bool fix = false,
    bool verbose = false,
  }) async {
    final dartFiles = await _findDartFiles();
    final issues = <ValidationIssue>[];

    for (final file in dartFiles) {
      if (verbose) {
        print('  Checking: ${file.path}');
      }

      final fileIssues = await _validateFile(file);
      issues.addAll(fileIssues);

      if (fix && fileIssues.isNotEmpty) {
        await _attemptFix(file, fileIssues);
      }
    }

    return ValidationResults(
      totalFiles: dartFiles.length,
      issues: issues,
      timestamp: DateTime.now(),
    );
  }

  /// Find all Dart files to validate
  Future<List<File>> _findDartFiles() async {
    final libDir = Directory(_libPath);
    if (!libDir.existsSync()) {
      throw Exception('lib directory not found');
    }

    final dartFiles = <File>[];
    await for (final entity in libDir.list(recursive: true)) {
      if (entity is File && entity.path.endsWith('.dart')) {
        // Skip generated files
        if (!entity.path.contains('.g.dart') &&
            !entity.path.contains('.freezed.dart') &&
            !entity.path.contains('generated/')) {
          dartFiles.add(entity);
        }
      }
    }

    return dartFiles;
  }

  /// Validate a single file
  Future<List<ValidationIssue>> _validateFile(File file) async {
    final content = await file.readAsString();
    final lines = content.split('\n');
    final issues = <ValidationIssue>[];

    for (int i = 0; i < lines.length; i++) {
      final line = lines[i];
      final lineNumber = i + 1;

      // Quick development checks
      issues.addAll(_checkPlatformUsage(file.path, line, lineNumber));
      issues.addAll(_checkDesignSystem(file.path, line, lineNumber));
      issues.addAll(_checkResponsiveDesign(file.path, line, lineNumber));
      issues.addAll(_checkChineseFonts(file.path, line, lineNumber, content));
    }

    return issues;
  }

  /// Check for platform-specific usage
  List<ValidationIssue> _checkPlatformUsage(
      String filePath, String line, int lineNumber) {
    final issues = <ValidationIssue>[];

    if (line.contains('Platform.isIOS') ||
        line.contains('Platform.isAndroid')) {
      issues.add(ValidationIssue(
        type: IssueType.platform,
        severity: IssueSeverity.error,
        file: filePath,
        line: lineNumber,
        message: 'Use PlatformAdaptations instead of direct Platform checks',
        quickFix: 'Replace with PlatformAdaptations.isIOS/isAndroid',
      ));
    }

    if (line.contains('MaterialPageRoute') ||
        line.contains('CupertinoPageRoute')) {
      issues.add(ValidationIssue(
        type: IssueType.navigation,
        severity: IssueSeverity.error,
        file: filePath,
        line: lineNumber,
        message: 'Use adaptive navigation',
        quickFix: 'Replace with AdaptiveNavigation.push()',
      ));
    }

    return issues;
  }

  /// Check design system compliance
  List<ValidationIssue> _checkDesignSystem(
      String filePath, String line, int lineNumber) {
    final issues = <ValidationIssue>[];

    // Check for hardcoded values
    final hardcodedPatterns = [
      RegExp(r'EdgeInsets\.all\(\s*\d+\.?\d*\s*\)'),
      RegExp(r'BorderRadius\.circular\(\s*\d+\.?\d*\s*\)'),
      RegExp(r'SizedBox\(height:\s*\d+\.?\d*\s*\)'),
    ];

    for (final pattern in hardcodedPatterns) {
      if (pattern.hasMatch(line) && !line.contains('DesignSystem')) {
        issues.add(ValidationIssue(
          type: IssueType.designSystem,
          severity: IssueSeverity.error,
          file: filePath,
          line: lineNumber,
          message: 'Use DesignSystem constants',
          quickFix: 'Replace with DesignSystem.spaceM, etc.',
        ));
        break;
      }
    }

    return issues;
  }

  /// Check responsive design
  List<ValidationIssue> _checkResponsiveDesign(
      String filePath, String line, int lineNumber) {
    final issues = <ValidationIssue>[];

    if (line.contains('MediaQuery.of(context).size') &&
        !line.contains('ResponsiveSystem')) {
      issues.add(ValidationIssue(
        type: IssueType.responsive,
        severity: IssueSeverity.info,
        file: filePath,
        line: lineNumber,
        message: 'Consider using ResponsiveSystem for tablet/phone adaptations',
        quickFix: 'Use ResponsiveSystem.isTablet()',
      ));
    }

    return issues;
  }

  /// Check Chinese font usage
  List<ValidationIssue> _checkChineseFonts(
      String filePath, String line, int lineNumber, String content) {
    final issues = <ValidationIssue>[];

    if (line.contains('fontFamily') &&
        RegExp(r'[\u4e00-\u9fff]').hasMatch(content) &&
        !content.contains('chinese_font_library')) {
      issues.add(ValidationIssue(
        type: IssueType.chineseFont,
        severity: IssueSeverity.warning,
        file: filePath,
        line: lineNumber,
        message: 'Use chinese_font_library for Chinese text',
        quickFix: 'Import chinese_font_library',
      ));
    }

    return issues;
  }

  /// Attempt to fix issues automatically
  Future<void> _attemptFix(File file, List<ValidationIssue> issues) async {
    var content = await file.readAsString();
    bool modified = false;

    for (final issue in issues) {
      if (issue.type == IssueType.platform) {
        content =
            content.replaceAll('Platform.isIOS', 'PlatformAdaptations.isIOS');
        content = content.replaceAll(
            'Platform.isAndroid', 'PlatformAdaptations.isAndroid');
        modified = true;
      }
    }

    if (modified) {
      await file.writeAsString(content);
      print('🔧 Auto-fixed: ${file.path}');
    }
  }

  /// Print validation results
  void _printResults(ValidationResults results) {
    if (results.issues.isEmpty) {
      print('✅ All checks passed! (${results.totalFiles} files)');
      return;
    }

    print('\n📊 Validation Results');
    print('Files: ${results.totalFiles}');
    print('Issues: ${results.issues.length}');

    final grouped = <IssueType, List<ValidationIssue>>{};
    for (final issue in results.issues) {
      grouped.putIfAbsent(issue.type, () => []).add(issue);
    }

    for (final entry in grouped.entries) {
      print(
          '\n${_getTypeIcon(entry.key)} ${_getTypeName(entry.key)} (${entry.value.length})');

      for (final issue in entry.value.take(3)) {
        final severity = _getSeverityIcon(issue.severity);
        print('  $severity ${issue.file}:${issue.line}');
        print('    ${issue.message}');
        if (issue.quickFix.isNotEmpty) {
          print('    💡 ${issue.quickFix}');
        }
      }

      if (entry.value.length > 3) {
        print('    ... and ${entry.value.length - 3} more');
      }
    }
  }

  String _getTypeIcon(IssueType type) {
    switch (type) {
      case IssueType.platform:
        return '📱';
      case IssueType.designSystem:
        return '🎯';
      case IssueType.navigation:
        return '🧭';
      case IssueType.responsive:
        return '📐';
      case IssueType.chineseFont:
        return '🈳';
    }
  }

  String _getTypeName(IssueType type) {
    switch (type) {
      case IssueType.platform:
        return 'Platform Issues';
      case IssueType.designSystem:
        return 'Design System';
      case IssueType.navigation:
        return 'Navigation';
      case IssueType.responsive:
        return 'Responsive Design';
      case IssueType.chineseFont:
        return 'Chinese Fonts';
    }
  }

  String _getSeverityIcon(IssueSeverity severity) {
    switch (severity) {
      case IssueSeverity.error:
        return '❌';
      case IssueSeverity.warning:
        return '⚠️';
      case IssueSeverity.info:
        return 'ℹ️';
    }
  }

  void printHelp() {
    print('''
DassoShu Reader Development Validator

Usage: dart scripts/dev_validation.dart [options]

Options:
  -h, --help      Show this help message
  -w, --watch     Watch mode - continuously monitor files
  -v, --verbose   Show detailed progress
  --fix           Attempt to automatically fix issues

Examples:
  dart scripts/dev_validation.dart
  dart scripts/dev_validation.dart --watch
  dart scripts/dev_validation.dart --fix --verbose
''');
  }
}

class ValidationResults {
  final int totalFiles;
  final List<ValidationIssue> issues;
  final DateTime timestamp;

  ValidationResults({
    required this.totalFiles,
    required this.issues,
    required this.timestamp,
  });

  bool get hasErrors => issues.any((i) => i.severity == IssueSeverity.error);
}

class ValidationIssue {
  final IssueType type;
  final IssueSeverity severity;
  final String file;
  final int line;
  final String message;
  final String quickFix;

  ValidationIssue({
    required this.type,
    required this.severity,
    required this.file,
    required this.line,
    required this.message,
    this.quickFix = '',
  });
}

enum IssueType { platform, designSystem, navigation, responsive, chineseFont }

enum IssueSeverity { error, warning, info }
